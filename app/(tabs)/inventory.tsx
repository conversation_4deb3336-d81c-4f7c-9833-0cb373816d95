import React, { useMemo } from 'react';
import { View, Text, useColorScheme } from 'react-native';
import { Appbar, Badge } from 'react-native-paper';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useRouter } from 'expo-router';
import { getThemeColors } from '@/styles/Theme';
import createInventoryStyles from '@/styles/InventoryStyles';
import { InventoryItem, InventoryCategory } from '@/components/types';
import { InventoryService } from '@/services/InventoryService';
import InventoryCategoryComponent from '@/components/InventoryCategory';
import LoadingAnimation from '@/components/LoadingAnimation';
import { Colors } from '@/constants/Colors';
import ShoppingCart from '../../assets/images/icons/shopping-cart.svg';
import Camerta from '@/assets/images/icons/camera.svg';
import { useGroceryList } from '@/contexts/GroceryListContext';
import { useInventory } from '@/contexts/InventoryContext';

export default function InventoryTab() {
  const colorScheme = useColorScheme() || 'light';
  const router = useRouter();
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  const styles = createInventoryStyles(colors);

  const { groceryItemCount } = useGroceryList();
  const { inventoryItems, categorizedInventory, loading, refreshInventory, updateLocalInventory } = useInventory();

  // Memoized icon components to prevent re-rendering and jiggling
  const CameraIcon = useMemo(() => () => <Camerta fill={Colors[colorScheme].tint} />, [colorScheme]);
  const ShoppingCartIcon = useMemo(() => () => <ShoppingCart fill={Colors[colorScheme].tint} />, [colorScheme]);

  // Handle incrementing item quantity
  const handleIncrement = async (itemName: string) => {
    try {
      const item = inventoryItems.find((i: InventoryItem) => i.name === itemName);
      if (item) {
        // Update local state immediately
        const updatedItems = inventoryItems.map((i: InventoryItem) => {
          if (i.name === itemName) {
            return { ...i, quantity: i.quantity + 1 };
          }
          return i;
        });

        // Update UI
        updateLocalInventory(updatedItems);

        // Update backend in the background
        InventoryService.updateItemQuantity(itemName, item.quantity + 1).catch((error) => {
          console.error('Error incrementing item:', error);
          // Revert to previous state on error
          refreshInventory();
        });
      }
    } catch (error) {
      console.error('Error incrementing item:', error);
    }
  };

  // Handle decrementing item quantity
  const handleDecrement = async (itemName: string) => {
    try {
      const item = inventoryItems.find((i: InventoryItem) => i.name === itemName);
      if (item) {
        if (item.quantity <= 1) {
          // If quantity is 1 or less, remove the item
          handleRemove(itemName);
        } else {
          // Update local state immediately
          const updatedItems = inventoryItems.map((i: InventoryItem) => {
            if (i.name === itemName) {
              return { ...i, quantity: i.quantity - 1 };
            }
            return i;
          });

          // Update UI
          updateLocalInventory(updatedItems);

          // Update backend in the background
          InventoryService.updateItemQuantity(itemName, item.quantity - 1).catch((error) => {
            console.error('Error decrementing item:', error);
            // Revert to previous state on error
            refreshInventory();
          });
        }
      }
    } catch (error) {
      console.error('Error decrementing item:', error);
    }
  };

  // Handle removing an item
  const handleRemove = async (itemName: string) => {
    try {
      // Update local state immediately
      const updatedItems = inventoryItems.filter((i: InventoryItem) => i.name !== itemName);

      // Update UI
      updateLocalInventory(updatedItems);

      // Update backend in the background
      InventoryService.removeItem(itemName).catch((error) => {
        console.error('Error removing item:', error);
        // Revert to previous state on error
        refreshInventory();
      });
    } catch (error) {
      console.error('Error removing item:', error);
    }
  };

  // Handle adding a new item
  const handleAddItem = async (itemName: string, categoryName: string) => {
    try {
      // Create new item with default quantity of 1
      const newItem: InventoryItem = {
        name: itemName,
        quantity: 1,
        addedAt: Date.now(),
      };

      // Update backend in the background
      InventoryService.addOrUpdateItem(newItem).catch((error) => {
        console.error('Error adding item:', error);
        // Revert to previous state on error
        refreshInventory();
      });

      // Update local state immediately
      const itemWithCategory = { ...newItem, category: categoryName };
      const updatedItems = [...inventoryItems, itemWithCategory];
      updateLocalInventory(updatedItems);
    } catch (error) {
      console.error('Error adding item:', error);
    }
  };

  // Render empty state
  const renderEmptyState = () => {
    // Create default categories for empty state
    const defaultCategories: InventoryCategory[] = [
      { name: 'Fruits', emoji: '🍓', items: [] },
      { name: 'Vegetables', emoji: '🥦', items: [] },
      { name: 'Proteins', emoji: '🥩', items: [] },
      { name: 'Grains & Cereals', emoji: '🌾', items: [] },
      { name: 'Dairy & Alternatives', emoji: '🥛', items: [] },
    ];

    return (
      <KeyboardAwareScrollView
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        keyboardShouldPersistTaps='always'
        style={styles.content}
      >
        <View style={styles.emptyStateContainer}>
          <Text style={styles.emptyStateTitle}>Your inventory is empty</Text>
          <Text style={styles.emptyStateText}>
            Add items to your inventory by taking a photo of your fridge or pantry or manually adding items below.
          </Text>
        </View>

        {defaultCategories.map((category, index) => (
          <InventoryCategoryComponent
            key={`${category.name}-${index}`}
            category={category}
            onIncrement={handleIncrement}
            onDecrement={handleDecrement}
            onRemove={handleRemove}
            onAddItem={handleAddItem}
          />
        ))}
      </KeyboardAwareScrollView>
    );
  };

  return (
    <View style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.Action icon={CameraIcon} onPress={() => router.push('/camera')} />
        <Appbar.Content title='' style={styles.title} />
        <View>
          <Appbar.Action
            icon={ShoppingCartIcon}
            onPress={() => {
              router.push('/grocery-list');
            }}
          />
          {groceryItemCount > 0 && (
            <Badge
              visible={true}
              size={20}
              style={{
                position: 'absolute',
                top: 5,
                right: 5,
                backgroundColor: colors.accent,
              }}
            >
              {groceryItemCount}
            </Badge>
          )}
        </View>
      </Appbar.Header>

      {loading ? (
        <LoadingAnimation
          source={require('../../assets/images/gifs/bounce-veggie.gif')}
          message='Sorting your snacks and veggies… one carrot at a time 🥕'
        />
      ) : inventoryItems.length === 0 ? (
        renderEmptyState()
      ) : (
        <KeyboardAwareScrollView
          enableOnAndroid={true}
          enableAutomaticScroll={true}
          keyboardShouldPersistTaps='always'
          style={styles.content}
        >
          {categorizedInventory.map((category: InventoryCategory, index: number) => (
            <InventoryCategoryComponent
              key={`${category.name}-${index}`}
              category={category}
              onIncrement={handleIncrement}
              onDecrement={handleDecrement}
              onRemove={handleRemove}
              onAddItem={handleAddItem}
            />
          ))}
        </KeyboardAwareScrollView>
      )}
    </View>
  );
}
